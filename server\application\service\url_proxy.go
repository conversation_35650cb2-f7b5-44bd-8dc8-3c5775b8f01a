package service

import (
	"socks/server/application/event"
	"socks/server/domain/entity"
)

type UrlProxyService interface {
	RegisterClient(client *entity.Client) error
	RegisterURLMapping(clientUUID, urlPath, baseURL, serviceName, serviceGroup string) error
	UnregisterURLMapping(clientUUID, urlPath, baseURL string) error
	GetURLMapping(urlPath string) *entity.URLMapping
	GetClientURLMappings(clientUUID string) []string
	UpdateOnlineStatus(clientUUID string, online bool) error
}

func GetUrlProxyService() UrlProxyService {
	return event.GetURLProxy()
}
