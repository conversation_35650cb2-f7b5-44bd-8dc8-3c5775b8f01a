package service

import (
	"fmt"
	"log"
	"socks/server/domain/entity"
	"socks/server/domain/event"
	"socks/server/util"
	"sync"
	"time"
)

var (
	urlProxyService *URLProxyService
	upsOnce         sync.Once
)

type URLProxyService struct {
	connection *event.Connection
	clients    map[string]*entity.Client
	lock       sync.RWMutex
}

func NewURLProxyService(config *util.TunnelConfig) *URLProxyService {
	return &URLProxyService{
		connection: event.GetConnection(config),
		clients:    make(map[string]*entity.Client),
	}
}

func GetUrlProxyService(config *util.TunnelConfig) *URLProxyService {
	upsOnce.Do(func() {
		urlProxyService = NewURLProxyService(config)
	})
	return urlProxyService
}

func (s *URLProxyService) RegisterClient(client *entity.Client) error {
	s.lock.Lock()
	defer s.lock.Unlock()

	if _, ok := s.clients[client.UUID]; ok {
		return fmt.Errorf("client alread registed, uuid: %s", client.UUID)
	}

	s.clients[client.UUID] = client
	return nil
}

// RegisterURLMapping 注册URL映射
func (s *URLProxyService) RegisterURLMapping(clientUUID, urlPath, baseURL, serviceName, serviceGroup string) error {
	// 检查客户端是否存在
	s.lock.RLock()
	defer s.lock.RUnlock()
	client, ok := s.clients[clientUUID]
	if !ok {
		return fmt.Errorf("client not found: %s", clientUUID)
	}

	// 检查URL路径是否已被注册
	existingMapping := s.connection.GetURLMapping(urlPath)
	if existingMapping != nil {
		// 如果存在，且属于同一个客户端，更新映射
		// 同名不同uuid客户端暂不考虑
		if existingMapping.Client.UUID == clientUUID && !existingMapping.IsBaseUrlExist(baseURL) {
			existingMapping.BaseURL[baseURL] = struct{}{}
			return nil
		}
		return fmt.Errorf("client base url path already registered in server path: %s", urlPath)
	}

	// 创建URL映射
	mapping := &entity.URLMapping{
		Name:         fmt.Sprintf("%s:%s", client.Name, urlPath),
		Client:       client,
		URLPath:      urlPath,
		BaseURL:      map[string]struct{}{baseURL: {}},
		Created:      time.Now(),
		Enable:       true,
		Online:       true,
		ServiceName:  serviceName,
		ServiceGroup: serviceGroup,
		Description:  fmt.Sprintf("URL mapping for %s", serviceName),
	}

	// 添加映射
	s.connection.AddURLMapping(mapping)
	return nil
}

// UnregisterURLMapping 取消注册URL映射
func (s *URLProxyService) UnregisterURLMapping(clientUUID, urlPath, baseURL string) error {
	// 检查映射是否存在
	mapping := s.connection.GetURLMapping(urlPath)
	if mapping == nil {
		return fmt.Errorf("URL mapping not found: %s", urlPath)
	}

	// 检查是否属于该客户端
	if mapping.Client.UUID != clientUUID {
		return fmt.Errorf("URL mapping does not belong to client: %s", clientUUID)
	}

	// 删除映射
	s.connection.DeleteURLMapping(urlPath)

	log.Printf("URL映射取消注册成功: %s (客户端: %s)", urlPath, clientUUID)
	return nil
}

// GetURLMapping 获取URL映射
func (s *URLProxyService) GetURLMapping(urlPath string) *entity.URLMapping {
	return s.connection.GetURLMapping(urlPath)
}

// GetClientURLMappings 获取客户端的所有URL映射
func (s *URLProxyService) GetClientURLMappings(clientUUID string) []string {
	return s.connection.GetClientURLMappings(clientUUID)
}

// UpdateOnlineStatus 更新在线状态
func (s *URLProxyService) UpdateOnlineStatus(clientUUID string, online bool) error {
	s.connection.UpdateURLOnlineStatus(clientUUID, online)
	return nil
}
