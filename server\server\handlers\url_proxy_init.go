package handlers

import (
	connEvent "socks/server/domain/event"
)

// 全局URL代理处理器实例
var globalURLProxyHandler *URLProxyHandler

// InitURLProxyHandler 初始化URL代理处理器并设置全局响应处理函数
func InitURLProxyHandler() *URLProxyHandler {
	if globalURLProxyHandler == nil {
		globalURLProxyHandler = GetURLProxyHandler()
		// 设置全局响应处理函数
		connEvent.URLProxyResponseHandler = globalURLProxyHandler.HandleProxyResponse
	}
	return globalURLProxyHandler
}

// GetGlobalURLProxyHandler 获取全局URL代理处理器
func GetGlobalURLProxyHandler() *URLProxyHandler {
	if globalURLProxyHandler == nil {
		return InitURLProxyHandler()
	}
	return globalURLProxyHandler
}
