package event

import (
	"fmt"
	"log"
	"net"
	"socks/server/domain/entity"
	"time"
)

const (
	ONLINE  = true
	OFFLINE = false
)

// 一个基于服务端端口层面的代理监听对象，包含：
// 1、服务端代理端口监听器
// 2、服务端和客户端的持久化通讯通道
// 3、系统内的conn对象（端口映射表、通道表、监听器表）
type ProxyTunnel struct {
	clientId   string
	clientPort int
	serverPort int
	tunnel     *entity.SafeConn // serverPort对应的转发通道
	listener   *entity.Listener // 监听serverPort的Listener
	conn       *Connection      // 系统内conn对象
	stop       chan bool
}

func NewProxyTunnel(conn *Connection, serverPort int, clientPort int, clientId string) *ProxyTunnel {
	return &ProxyTunnel{
		clientId:   clientId,
		clientPort: clientPort,
		serverPort: serverPort,
		tunnel:     conn.GetTunnelByServerPort(serverPort),
		listener:   conn.GetListener(serverPort),
		conn:       conn,
		stop:       make(chan bool),
	}

}

func (p *ProxyTunnel) StartProxy() {
	p.conn.UpdateOnlineStatus(p.clientId, p.clientPort, ONLINE)
	for {
		select {
		case <-p.stop:
			p.conn.CloseListener(p.serverPort)
			return
		default:
			requestConn, err := p.listener.GetListener().Accept()
			if err != nil {
				log.Printf("Accept error on ps=%d: %v", p.serverPort, err)
				return
			}
			go p.StartTransData2RequestClient(requestConn)
		}
	}
}

func (p *ProxyTunnel) StopProxy() error {
	p.conn.UpdateOnlineStatus(p.clientId, p.clientPort, ONLINE)
	p.stop <- true
	return nil
}

// 转发消息至发起请求的客户端
func (p *ProxyTunnel) StartTransData2RequestClient(conn net.Conn) {
	defer conn.Close()
	// Generate unique ID
	chanId := fmt.Sprintf("%d-%d-%d", time.Now().UnixNano(), conn.RemoteAddr().(*net.TCPAddr).Port, p.serverPort)
	p.addRespChan(chanId)

	// Notify client to open local connection
	if err := p.tunnel.WriteJSON(entity.ConnMessage{ID: chanId, Type: "open"}); err == nil {
		// TCP -> Control channel
		go func() {
			buf := make([]byte, 4096)
			for {
				n, err := conn.Read(buf)
				if n > 0 {
					p.tunnel.WriteJSON(entity.ConnMessage{ID: chanId, Type: "data", Data: buf[:n]})
				}
				if err != nil {
					break
				}
			}
			p.tunnel.WriteJSON(entity.ConnMessage{ID: chanId, Type: "close"})
		}()
	} else {
		log.Printf("write to tunnel err: %v", err)
	}

	// Control channel -> TCP
	for data := range p.getResponseChan(chanId) {
		conn.Write(data)
	}

	// Cleanup
	p.deleteRespChan(chanId)
}

func (p *ProxyTunnel) addRespChan(id string) {
	ch := make(chan []byte, 100)
	p.tunnel.AddRespChan(id, ch)
}

func (p *ProxyTunnel) getResponseChan(id string) chan []byte {
	return p.tunnel.GetResponseChan(id)
}

func (p *ProxyTunnel) deleteRespChan(id string) error {
	return p.tunnel.DeleteRespChan(id)
}
