package entity

import (
	"fmt"
	"net"
	"socks/server/util"
	"strings"
	"sync"
	"time"
)

const (
	EMPTY_MAPPING_ID = -1
)

var (
	portmappingGroup *PortMappingGroup
	pmgOnce          sync.Once
	listenerGroup    *ListenerGroup
	lgOnce           sync.Once
	urlmappingGroup  *URLMappingGroup
	umgOnce          sync.Once
)

type Protocol struct {
	TCP bool
	UDP bool
}

type Listener struct {
	ProxyListener net.Listener
}

type ListenerGroup struct {
	Listeners map[int]*Listener
	Locker    sync.RWMutex
}

// PortMapping 存储端口映射信息
type PortMapping struct {
	ID          int    // 数据库记录id
	Name        string // 隧道名称 主机名称:代理端口
	Client      *Client
	ClientPort  int       // 本地端口
	ServerPort  int       // 公网端口
	Listener    *Listener // 监听器
	Created     time.Time // 创建时间
	Enable      bool      // 代理是否启用
	Online      bool      // 是否在线
	Encryption  bool      // 是否加密
	Password    string    // 认证密钥
	RateLimit   int       // 最大连接数
	ServiceName string    // 具体端口对应的服务名称，不可为空
	Description string    // 代理描述
	Protocol    *Protocol // 当前端口映射的协议
}

// URLMapping 存储URL路径映射信息
type URLMapping struct {
	ID           int                 // 数据库记录id
	Name         string              // 映射名称
	Client       *Client             // 客户端信息
	URLPath      string              // 服务端代理的客户端URL路径，单客户端、服务唯一
	BaseURL      map[string]struct{} // 服务端代理的客户端服务URL根路径，如 /user/info
	Created      time.Time           // 创建时间
	Enable       bool                // 映射是否启用
	Online       bool                // 是否在线
	ServiceName  string              // 服务名称
	ServiceGroup string              // 服务组别
	Description  string              // 映射描述
	locker       sync.RWMutex
}

type PortMappingGroup struct {
	MinPort           int
	MaxPort           int
	Timeout           int
	MaxConnection     int
	SlidingExpiration int
	Mappings          map[string]*PortMapping // 客户端uuid+端口映射一张PortMapping表，实现持久化
	ServerPortMapping map[int]string          //代理服务端端口和客户端uuid的映射关系，方便快速查询
	Locker            sync.RWMutex
}

// URLMappingGroup 管理URL映射
type URLMappingGroup struct {
	Mappings    map[string]*URLMapping // URL路径到映射的关系，key为URLPath
	ClientPaths map[string][]string    // 客户端UUID到其注册的URL路径列表的映射
	Locker      sync.RWMutex
}

func (p *Protocol) GetProtocolType() string {
	if p.TCP && p.UDP {
		return "TCP,UDP"
	} else if p.TCP {
		return "TCP"
	} else if p.UDP {
		return "UDP"
	} else {
		return "未知"
	}
}

func BuildProtocol(protocolType string) *Protocol {
	p := &Protocol{}
	if strings.Contains(protocolType, "TCP") {
		p.TCP = true
	}

	if strings.Contains(protocolType, "UDP") {
		p.UDP = true
	}

	return p
}

func GetListenerGroup() *ListenerGroup {
	lgOnce.Do(func() {
		listenerGroup = &ListenerGroup{
			Listeners: make(map[int]*Listener),
		}
	})
	return listenerGroup
}

func GetPortMappingGroup(config *util.TunnelConfig) *PortMappingGroup {
	pmgOnce.Do(func() {
		portmappingGroup = &PortMappingGroup{
			MinPort:           config.MinPort,
			MaxPort:           config.MaxPort,
			Timeout:           config.Timeout,
			MaxConnection:     config.MaxConnection,
			SlidingExpiration: config.SlidingExpiration,
			Mappings:          make(map[string]*PortMapping),
			ServerPortMapping: make(map[int]string),
		}
	})

	return portmappingGroup
}

func GetURLMappingGroup() *URLMappingGroup {
	umgOnce.Do(func() {
		urlmappingGroup = &URLMappingGroup{
			Mappings:    make(map[string]*URLMapping),
			ClientPaths: make(map[string][]string),
		}
	})
	return urlmappingGroup
}

func (l *Listener) BuildListener(netType string, ip string, port int) error {

	address := fmt.Sprintf("%s:%d", ip, port)

	switch netType {
	case "tcp", "tcp4", "tcp6":
		ln, err := net.Listen(netType, address)
		if err != nil {
			return fmt.Errorf("创建 %s 监听器失败: %v", netType, err)
		}
		l.ProxyListener = ln

	case "udp", "udp4", "udp6":
		// UDP 不使用 net.Listener，而是使用 net.PacketConn
		// 这里我们返回错误，因为 Listener 结构体当前设计用于 TCP
		return fmt.Errorf("UDP 协议不支持 net.Listener 接口，请使用 PacketConn")

	default:
		return fmt.Errorf("不支持的网络类型: %s", netType)
	}

	return nil
}

func (l Listener) GetListener() net.Listener {
	return l.ProxyListener
}

func (g *ListenerGroup) AddListener(serverPort int, listener *Listener) {
	g.Locker.Lock()
	defer g.Locker.Unlock()
	g.Listeners[serverPort] = listener
}

func (g *ListenerGroup) GetListener(serverPort int) *Listener {
	g.Locker.RLock()
	defer g.Locker.RUnlock()
	if listener, ok := g.Listeners[serverPort]; ok {
		return listener
	}
	return nil
}

func (g *ListenerGroup) DeleteListener(serverPort int) {
	g.Locker.Lock()
	defer g.Locker.Unlock()
	if listener, ok := g.Listeners[serverPort]; ok {
		listener.ProxyListener.Close()
	}
	delete(g.Listeners, serverPort)
}

func (g *PortMappingGroup) AddMapping(clientUUID string, clientPort int, mapping *PortMapping) {
	g.Locker.Lock()
	defer g.Locker.Unlock()
	cacheKey := fmt.Sprintf("%s:%d", clientUUID, clientPort)
	g.Mappings[cacheKey] = mapping
	g.ServerPortMapping[mapping.ServerPort] = clientUUID
}

func (g *PortMappingGroup) GetMapping(clientUUID string, clientPort int) *PortMapping {
	g.Locker.RLock()
	defer g.Locker.RUnlock()
	cacheKey := fmt.Sprintf("%s:%d", clientUUID, clientPort)
	if mapping, ok := g.Mappings[cacheKey]; ok {
		return mapping
	}
	return nil
}

func (g *PortMappingGroup) DeleteMapping(clientUUID string, clientPort int) int {
	g.Locker.Lock()
	defer g.Locker.Unlock()
	mapping := g.GetMapping(clientUUID, clientPort)
	if mapping == nil {
		return EMPTY_MAPPING_ID
	}
	// 必须删除ServerPortMapping这个map，通过它来判断端口是否已经分配
	delete(g.ServerPortMapping, mapping.ServerPort)
	cacheKey := fmt.Sprintf("%s:%d", clientUUID, clientPort)
	delete(g.Mappings, cacheKey)
	return mapping.ID
}

func (g *PortMappingGroup) GetClientUUIDbyServerPort(serverPort int) string {
	return g.ServerPortMapping[serverPort]
}

func (g *PortMappingGroup) UpdateOnlineStatus(clientUUID string, clientPort int, online bool) {
	g.Locker.Lock()
	defer g.Locker.Unlock()

	cacheKey := fmt.Sprintf("%s:%d", clientUUID, clientPort)
	if mapping, ok := g.Mappings[cacheKey]; ok {
		mapping.Online = online
	}
}

func (g *PortMappingGroup) UpdateOnlineStatusByClientStatus(uuid string, online bool) {
	g.Locker.Lock()
	defer g.Locker.Unlock()
	for _, mapping := range g.Mappings {
		if mapping.Client.UUID == uuid {
			mapping.Online = online
		}
	}
}

func (m *URLMapping) IsBaseUrlExist(baseURL string) bool {
	m.locker.RLock()
	defer m.locker.RUnlock()
	_, ok := m.BaseURL[baseURL]
	return ok
}

func (m *URLMapping) DeleteBaseUrl(baseURL string) {
	m.locker.Lock()
	defer m.locker.Unlock()
	delete(m.BaseURL, baseURL)
}

func (m *URLMapping) MatchBaseURL(targetURL string) string {
	m.locker.RLock()
	defer m.locker.RUnlock()

	var longestMatch string
	maxLen := -1

	for baseURL := range m.BaseURL {
		// 确保baseURL是targetURL的前缀
		if strings.HasPrefix(targetURL, baseURL) {
			// 找出最长匹配
			if len(baseURL) > maxLen {
				maxLen = len(baseURL)
				longestMatch = baseURL
			}
		}
	}

	return longestMatch
}

// URLMappingGroup 管理方法
func (g *URLMappingGroup) AddMapping(mapping *URLMapping) {
	g.Locker.Lock()
	defer g.Locker.Unlock()
	g.Mappings[mapping.URLPath] = mapping

	// 更新客户端路径映射
	clientUUID := mapping.Client.UUID
	if paths, exists := g.ClientPaths[clientUUID]; exists {
		// 检查路径是否已存在
		for _, path := range paths {
			if path == mapping.URLPath {
				return // 路径已存在，不重复添加
			}
		}
		g.ClientPaths[clientUUID] = append(paths, mapping.URLPath)
	} else {
		g.ClientPaths[clientUUID] = []string{mapping.URLPath}
	}
}

func (g *URLMappingGroup) GetMapping(urlPath string) *URLMapping {
	g.Locker.RLock()
	defer g.Locker.RUnlock()
	if mapping, ok := g.Mappings[urlPath]; ok {
		return mapping
	}
	return nil
}

func (g *URLMappingGroup) DeleteMapping(urlPath string) {
	g.Locker.Lock()
	defer g.Locker.Unlock()

	if mapping, exists := g.Mappings[urlPath]; exists {
		clientUUID := mapping.Client.UUID
		delete(g.Mappings, urlPath)

		// 从客户端路径映射中删除
		if paths, exists := g.ClientPaths[clientUUID]; exists {
			newPaths := make([]string, 0, len(paths)-1)
			for _, path := range paths {
				if path != urlPath {
					newPaths = append(newPaths, path)
				}
			}
			if len(newPaths) == 0 {
				delete(g.ClientPaths, clientUUID)
			} else {
				g.ClientPaths[clientUUID] = newPaths
			}
		}
	}
}

func (g *URLMappingGroup) DeleteMappingBaseURL(baseURL string) {
	g.Locker.Lock()
	defer g.Locker.Unlock()

	if mapping, exists := g.Mappings[baseURL]; exists {
		mapping.DeleteBaseUrl(baseURL)
	}

}

func (g *URLMappingGroup) GetClientPaths(clientUUID string) []string {
	g.Locker.RLock()
	defer g.Locker.RUnlock()
	if paths, exists := g.ClientPaths[clientUUID]; exists {
		// 返回副本以避免并发修改
		result := make([]string, len(paths))
		copy(result, paths)
		return result
	}
	return nil
}

func (g *URLMappingGroup) UpdateOnlineStatusByClientStatus(uuid string, online bool) {
	g.Locker.Lock()
	defer g.Locker.Unlock()
	for _, mapping := range g.Mappings {
		if mapping.Client.UUID == uuid {
			mapping.Online = online
		}
	}
}
