package handlers

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"socks/server/application/service"
	"socks/server/domain/entity"
	"strings"
	"sync"
	"time"
)

type URLProxyHandler struct {
	urlProxyService service.UrlProxyService
	tunnels         *entity.TunnelGroup
	responseWaiters map[string]chan *entity.URLProxyMessage // 等待响应的通道
	waitersLock     sync.RWMutex
}

func GetURLProxyHandler() *URLProxyHandler {
	handler := &URLProxyHandler{
		urlProxyService: service.GetUrlProxyService(),
		tunnels:         entity.GetTunnelGroup(),
		responseWaiters: make(map[string]chan *entity.URLProxyMessage),
	}

	// 设置全局响应处理函数
	// 注意：这里需要导入 connEvent "socks/server/domain/event"
	// connEvent.URLProxyResponseHandler = handler.HandleProxyResponse

	return handler
}

// RegisterURLHandler 处理URL注册请求
func (h *URLProxyHandler) RegisterURLHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	type RegisterRequest struct {
		ApiType      string `json:"api_type"`      // Agent, AI, or API
		ServiceGroup string `json:"service_group"` // 服务组别
		ServiceName  string `json:"service_name"`  // 服务名称
		ClientName   string `json:"client_name"`   // 客户端名称
		ClientUUID   string `json:"client_id"`     // 客户端UUID
		BaseURL      string `json:"base_url"`      // 基础URL，客户端服务的基础路径
	}

	var req RegisterRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "无效的请求体格式", http.StatusBadRequest)
		return
	}

	if req.ClientUUID == "" {
		http.Error(w, "missing client_id", http.StatusBadRequest)
		return
	}

	if req.ApiType == "" {
		http.Error(w, "missing api_type", http.StatusBadRequest)
		return
	}

	if req.ServiceName == "" {
		http.Error(w, "missing service_name", http.StatusBadRequest)
		return
	}

	if req.BaseURL == "" {
		http.Error(w, "missing base_url", http.StatusBadRequest)
		return
	}

	// 根据客户端类型生成代理路径
	var urlPath string
	switch req.ApiType {
	case "Agent":
		urlPath = fmt.Sprintf("/Agent/%s/%s", req.ClientName, req.ServiceName)
	case "AI":
		urlPath = fmt.Sprintf("/AI/%s/%s/%s", req.ServiceGroup, req.ServiceName, req.ClientName)
	case "API":
		urlPath = fmt.Sprintf("/API/%s/%s/%s", req.ServiceGroup, req.ServiceName, req.ClientName)
	default:
		http.Error(w, "invalid client_type", http.StatusBadRequest)
		return
	}

	// 注册URL映射，使用通配符匹配所有子路径
	err := h.urlProxyService.RegisterURLMapping(
		req.ClientUUID,
		urlPath,
		req.BaseURL,
		req.ServiceName,
		req.ServiceGroup,
	)

	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// 返回成功响应
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success":       true,
		"url_path":      urlPath,
		"base_url":      req.BaseURL,
		"service_name":  req.ServiceName,
		"api_type":      req.ApiType,
		"service_group": req.ServiceGroup,
	})

	log.Printf("URL register success: %s -> %s (client: %s)", urlPath, req.BaseURL, req.ClientUUID)
}

// UnregisterURLHandler 处理URL取消注册请求
func (h *URLProxyHandler) UnregisterURLHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodDelete {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 解析请求参数
	clientUUID := r.URL.Query().Get("client_id")
	if clientUUID == "" {
		http.Error(w, "missing client_id", http.StatusBadRequest)
		return
	}

	urlPath := r.URL.Query().Get("url_path")
	if urlPath == "" {
		http.Error(w, "missing url_path", http.StatusBadRequest)
		return
	}

	baseURL := r.URL.Query().Get("base_url")
	if baseURL == "" {
		http.Error(w, "missing base_url", http.StatusBadRequest)
		return
	}

	// 取消注册URL映射
	err := h.urlProxyService.UnregisterURLMapping(clientUUID, urlPath, baseURL)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// 返回成功响应
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success":  true,
		"url_path": urlPath,
	})

	log.Printf("URL映射取消注册成功: %s (客户端: %s)", urlPath, clientUUID)
}

// ProxyHandler 处理代理请求
func (h *URLProxyHandler) ProxyHandler(w http.ResponseWriter, r *http.Request) {
	// 获取请求路径
	url := r.URL.Path
	queryParam := r.URL.RawQuery

	// 查找URL映射
	var mapping *entity.URLMapping
	urlParts := strings.Split(strings.Trim(url, "/"), "/")
	apiType := strings.ToLower(urlParts[0])
	// 根据客户端类型查找匹配的映射
	switch apiType {
	case "agent":
		if len(urlParts) >= 3 {
			// Agent路径格式: /Agent/客户端名称/服务名称/...
			urlPath := fmt.Sprintf("/%s/%s/%s", urlParts[0], urlParts[1], urlParts[2])
			mapping = h.urlProxyService.GetURLMapping(urlPath)
		}
	case "ai", "api":
		if len(urlParts) >= 4 {
			// AI/API路径格式: /(AI|API)/服务组别/服务名称/客户端名称/...
			urlPath := fmt.Sprintf("/%s/%s/%s/%s", urlParts[0], urlParts[1], urlParts[2], urlParts[3])
			mapping = h.urlProxyService.GetURLMapping(urlPath)
		}
	}

	if mapping == nil {
		http.Error(w, "URL mapping not found", http.StatusNotFound)
		return
	}

	// 检查映射是否在线
	if !mapping.Online {
		http.Error(w, "Service offline", http.StatusServiceUnavailable)
		return
	}

	// 获取客户端连接
	tunnel := h.tunnels.GetTunnel(mapping.Client.UUID)
	if tunnel == nil {
		http.Error(w, "Client not connected", http.StatusServiceUnavailable)
		return
	}

	// 构建转发URL路径
	var targetPath string
	switch apiType {
	case "agent":
		if len(urlParts) > 3 {
			targetPath = "/" + strings.Join(urlParts[3:], "/")
		} else {
			targetPath = "/"
		}
	case "ai", "api":
		if len(urlParts) > 4 {
			targetPath = "/" + strings.Join(urlParts[4:], "/")
		} else {
			targetPath = "/"
		}
	}

	baseURL := mapping.MatchBaseURL(targetPath)
	if queryParam != "" {
		targetPath = targetPath + "?" + queryParam
	}

	// 构建代理请求消息  1.带回去匹配到的根路径，用来确定端口 2.带回去完整路径，用到访问具体端口
	proxyRequest := entity.URLProxyMessage{
		ID:        generateRequestID(),
		Type:      "proxy_request",
		BaseURL:   baseURL, // 使用处理后的目标路径
		TargetURL: targetPath,
		Method:    r.Method,
		Headers:   make(map[string]string),
		Body:      nil,
	}

	// 复制请求头
	for name, values := range r.Header {
		if len(values) > 0 {
			proxyRequest.Headers[name] = values[0]
		}
	}

	// 读取请求体
	if r.Body != nil {
		body, err := io.ReadAll(r.Body)
		if err != nil {
			http.Error(w, "Failed to read request body", http.StatusInternalServerError)
			return
		}
		proxyRequest.Body = body
	}

	// 序列化代理请求
	proxyData, err := json.Marshal(proxyRequest)
	if err != nil {
		http.Error(w, "Failed to serialize proxy request", http.StatusInternalServerError)
		return
	}

	// 构建消息
	message := entity.ConnMessage{
		ID:   proxyRequest.ID,
		Type: "proxy_request",
		Data: proxyData,
	}

	// 发送代理请求到客户端
	err = tunnel.WriteJSON(message)
	if err != nil {
		http.Error(w, "Failed to send request to client", http.StatusInternalServerError)
		return
	}

	// 创建响应等待通道
	responseChan := make(chan *entity.URLProxyMessage, 1)
	h.waitersLock.Lock()
	h.responseWaiters[proxyRequest.ID] = responseChan
	h.waitersLock.Unlock()

	// 等待客户端响应（30秒超时）
	select {
	case response := <-responseChan:
		// 清理等待通道
		h.waitersLock.Lock()
		delete(h.responseWaiters, proxyRequest.ID)
		h.waitersLock.Unlock()

		// 处理响应
		if response.Error != "" {
			http.Error(w, response.Error, http.StatusInternalServerError)
			return
		}

		// 设置响应头
		for name, value := range response.Headers {
			w.Header().Set(name, value)
		}

		// 设置状态码
		w.WriteHeader(response.Status)

		// 写入响应体
		if len(response.Body) > 0 {
			w.Write(response.Body)
		}

		log.Printf("代理请求完成: %s -> %s%s (状态码: %d)", baseURL, mapping.BaseURL, targetPath, response.Status)

	case <-time.After(30 * time.Second):
		// 清理等待通道
		h.waitersLock.Lock()
		delete(h.responseWaiters, proxyRequest.ID)
		h.waitersLock.Unlock()

		http.Error(w, "Request timeout", http.StatusGatewayTimeout)
		log.Printf("代理请求超时: %s -> %s%s", baseURL, mapping.BaseURL, targetPath)
	}
}

// HandleProxyResponse 处理URL代理响应
func (h *URLProxyHandler) HandleProxyResponse(response *entity.URLProxyMessage) {
	h.waitersLock.RLock()
	responseChan, exists := h.responseWaiters[response.ID]
	h.waitersLock.RUnlock()

	if !exists {
		log.Printf("未找到等待响应的通道: %s", response.ID)
		return
	}

	// 发送响应到等待通道
	select {
	case responseChan <- response:
		log.Printf("成功转发URL代理响应: %s", response.ID)
	default:
		log.Printf("响应通道已满，无法转发响应: %s", response.ID)
	}
}

// generateRequestID 生成请求ID
func generateRequestID() string {
	// 简单的ID生成，实际应用中可以使用UUID
	return fmt.Sprintf("req_%d", time.Now().UnixNano())
}
