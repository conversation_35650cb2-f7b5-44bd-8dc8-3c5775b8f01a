package event

import (
	"net"
	"socks/server/domain/entity"
	"socks/server/domain/service"
	"socks/server/util"
	"sync"
)

var (
	portProxy *PortProxy
	ppOnce    sync.Once
)

type PortProxy struct {
	proxyService *service.ProxyService
}

func GetPortProxy(config *util.TunnelConfig) *PortProxy {
	ppOnce.Do(func() {
		portProxy = &PortProxy{
			proxyService: service.GetProxyService(config),
		}
	})

	return portProxy
}

func (p *PortProxy) RegisterClient(client *entity.Client, conn net.Conn) error {
	return p.proxyService.RegisterClient(client, conn)
}

func (p *PortProxy) AllocateServerPort(clientUUID string, clientPort int, serviceName string) (int, error) {
	return p.proxyService.AllocateAvailablePort(clientUUID, clientPort, serviceName)
}

func (p *PortProxy) Start(clientUUID string, clientPort int, serverPort int) error {
	return p.proxyService.StartProxy(clientUUID, clientPort, serverPort)
}

func (p *PortProxy) Stop(serverPort int) error {
	return p.proxyService.StopProxy(serverPort)
}

func (p *PortProxy) RecoverFromDB() error {
	return p.proxyService.RecoverFromDB()
}

func (p *PortProxy) UpdateTunnelConfig(ids []int, all bool) error {
	return p.proxyService.UpdateTunnelConfig(ids, all)
}
