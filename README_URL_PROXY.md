# URL路径代理功能使用说明

## 功能概述

新增的URL路径代理功能允许通过URL路径将请求转发到客户端的具体应用。现在支持基于客户端类型的路径生成和通配符匹配。

### 现有功能（端口映射）
- 访问 `server:port` 直接转发到 `client:localPort`
- 基于端口的代理转发

### 新增功能（URL路径映射）
- 访问 `server:apiPort/path` 转发到客户端的具体应用
- 基于客户端类型的URL路径生成
- 支持通配符路径匹配
- 基于URL路径的代理转发

## 路径生成规则

根据客户端类型，URL路径按以下规则生成：

1. Agent客户端: `/Agent/clientName/serviceName`
2. AI客户端: `/AI/serviceGroup/serviceName/clientName`
3. API客户端: `/API/serviceGroup/serviceName/clientName`

所有生成的路径都支持通配符匹配，例如：
- `/API/group1/service1/client1/subpath`
- `/AI/group2/service2/client2/extra/path`

## 使用流程

### 1. 启动服务

#### 启动Server端
```bash
cd server
go run main.go -config=path/to/config.json
```

#### 启动Client端
```bash
cd client
go run main.go -server=<server_ip> -port=<server_port> -manager=8090
```

### 2. 注册URL映射

客户端提供HTTP接口供其他应用注册URL映射。现在支持JSON格式的请求体：

```bash
# 注册Agent类型URL映射
curl -X POST "http://localhost:8090/url/register" \
  -H "Content-Type: application/json" \
  -d '{
    "client_type": "Agent",
    "client_name": "agent1",
    "service_name": "service1",
    "base_url": "http://localhost:8080/service1"
  }'

# 注册AI类型URL映射
curl -X POST "http://localhost:8090/url/register" \
  -H "Content-Type: application/json" \
  -d '{
    "client_type": "AI",
    "client_name": "ai1",
    "service_name": "service2",
    "service_group": "group1",
    "base_url": "http://localhost:8080/service2"
  }'

# 注册API类型URL映射
curl -X POST "http://localhost:8090/url/register" \
  -H "Content-Type: application/json" \
  -d '{
    "client_type": "API",
    "client_name": "api1",
    "service_name": "service3",
    "service_group": "group2",
    "base_url": "http://localhost:8080/service3"
  }'

# 响应示例
{
  "success": true,
  "url_path": "/API/group2/service3/api1",
  "base_url": "http://localhost:8080/service3"
}
```

### 3. 使用代理

注册成功后，可以通过server端访问。URL路径根据客户端类型自动生成：

```bash
# 访问Agent服务
curl "http://<server_ip>:<api_port>/Agent/agent1/service1"

# 访问AI服务
curl "http://<server_ip>:<api_port>/AI/group1/service2/ai1"

# 访问API服务（支持子路径）
curl "http://<server_ip>:<api_port>/API/group2/service3/api1"
curl "http://<server_ip>:<api_port>/API/group2/service3/api1/subpath"
```

请求流程：
1. 请求发送到server，根据URL路径匹配映射
2. Server查找URL映射，找到对应的客户端
3. Server将请求转发给客户端
4. 客户端向本地应用发起请求
5. 客户端将响应返回给server
6. Server将响应返回给原始请求者

### 4. 取消注册URL映射

```bash
# 取消注册URL映射
curl -X DELETE "http://localhost:8090/url/unregister" \
  -H "Content-Type: application/json" \
  -d '{
    "client_type": "API",
    "client_name": "api1",
    "service_name": "service3",
    "service_group": "group2"
  }'

# 响应示例
{
  "success": true,
  "url_path": "/API/group2/service3/api1"
}
```

## 注意事项

1. **URL路径唯一性**: 
   - 每个URL路径只能被一个客户端注册
   - 路径根据客户端类型自动生成
   
2. **客户端类型**:
   - Agent: 简单路径格式，适用于代理服务
   - AI: 支持服务组分类的AI服务
   - API: 支持服务组分类和子路径的API服务

3. **服务组**:
   - Agent类型不需要服务组
   - AI和API类型必须提供服务组

4. **通配符匹配**:
   - 所有注册的路径自动支持子路径
   - 子路径请求会被转发到相应的base URL

5. **错误处理**:
   - 注册时验证必要字段
   - 路径冲突时返回错误
   - 客户端离线时返回503错误

## 故障排除

### 常见问题

1. **URL映射注册失败**
   - 检查请求格式是否正确
   - 确认所有必要字段都已提供
   - 检查URL路径是否已被其他客户端注册
   - 确认客户端已成功连接到server

2. **代理请求失败**
   - 检查URL路径格式是否符合客户端类型
   - 确认目标URL是否可访问
   - 确认客户端服务正常运行
   - 查看客户端和server端日志

3. **子路径不可访问**
   - 确认base URL配置正确
   - 检查目标服务是否支持子路径
   - 验证路径匹配规则
